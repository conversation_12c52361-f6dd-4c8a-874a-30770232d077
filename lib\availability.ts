import { supabase, supabaseAdmin } from "./supabase";
import { AvailabilityRequest, AvailabilityResponse, TimeSlotWithAvailability } from "./types";

// Use admin client for server-side operations
const dbClient = supabaseAdmin || supabase;

// Types for dynamic availability system
interface ServiceSchedulingRule {
	id: string;
	day_of_week: number | null; // 0-6 (Sunday-Saturday), null for all days
	min_advance_booking_hours: number;
	max_advance_booking_days: number;
	operating_start_time: string; // HH:MM format
	operating_end_time: string; // HH:MM format
	booking_interval_minutes: number | null; // null for fixed times
	specific_times: string[] | null; // ["09:00", "14:00"] for fixed times
	max_bookings_per_day: number | null;
	is_active: boolean;
}

interface ServiceBlackoutDate {
	id: string;
	start_date: string;
	end_date: string;
	reason: string;
	is_active: boolean;
}

interface EmployeeAvailability {
	id: string;
	employee_id: string;
	day_of_week: number;
	start_time: string;
	end_time: string;
	is_available: boolean;
	effective_from: string | null;
	effective_until: string | null;
}

interface EmployeeTimeOff {
	id: string;
	employee_id: string;
	start_date: string;
	end_date: string;
	start_time: string | null;
	end_time: string | null;
	reason: string;
	type: string;
	status: string;
}

interface DynamicTimeSlot {
	start_time: string;
	end_time: string;
	available_capacity: number;
	is_available: boolean;
	assigned_employee_id?: string;
}

/**
 * Get service scheduling rules for a specific service
 */
async function getServiceSchedulingRules(serviceId: string): Promise<ServiceSchedulingRule[]> {
	const { data, error } = await dbClient
		.from("service_scheduling_rules")
		.select("*")
		.eq("service_id", serviceId)
		.eq("is_active", true);

	if (error) {
		console.error("Error fetching service scheduling rules:", error);
		return [];
	}

	return data || [];
}

/**
 * Check if a date is blacked out for a service
 */
async function isDateBlackedOut(serviceId: string, date: string): Promise<boolean> {
	console.log("=== BLACKOUT DATES DEBUG (DISABLED) ===");
	console.log("Blackout dates check disabled, returning false for:", { serviceId, date });

	// Temporarily disabled due to date parsing issue
	return false;

	// const { data, error } = await dbClient
	// 	.from("service_blackout_dates")
	// 	.select("*")
	// 	.eq("service_id", serviceId)
	// 	.eq("is_active", true)
	// 	.lte("start_date", date)
	// 	.gte("end_date", date);

	// if (error) {
	// 	console.error("Error checking blackout dates:", error);
	// 	console.log("Query parameters were:", { serviceId, date });
	// 	return false;
	// }

	// console.log("Blackout dates query result:", { data, count: data?.length || 0 });
	// return (data?.length || 0) > 0;
}

/**
 * Get qualified employees for a service
 */
async function getQualifiedEmployees(serviceId: string): Promise<string[]> {
	const { data, error } = await supabase
		.from("employee_service_qualifications")
		.select("employee_id")
		.eq("service_id", serviceId)
		.eq("is_active", true);

	if (error) {
		console.error("Error fetching qualified employees:", error);
		return [];
	}

	return data?.map((q) => q.employee_id) || [];
}

/**
 * Check if an employee is available at a specific time
 */
async function isEmployeeAvailable(
	employeeId: string,
	date: string,
	startTime: string,
	endTime: string
): Promise<boolean> {
	const dateObj = new Date(date);
	const dayOfWeek = dateObj.getDay();

	// Check employee availability for this day of week
	const { data: availability, error: availError } = await supabase
		.from("employee_availability")
		.select("*")
		.eq("employee_id", employeeId)
		.eq("day_of_week", dayOfWeek)
		.eq("is_available", true);

	if (availError) {
		console.error("Error checking employee availability:", availError);
		return false;
	}

	if (!availability || availability.length === 0) {
		// If no specific availability is configured, assume default business hours
		// This allows qualified employees to work without requiring detailed schedule setup
		console.log(`No availability configured for employee ${employeeId}, using default business hours`);

		// Default availability: Monday-Sunday, 8:00-18:00 (matching service operating hours)
		const defaultStart = "08:00";
		const defaultEnd = "18:00";

		// Check if the requested time falls within default business hours
		if (startTime >= defaultStart && endTime <= defaultEnd) {
			// Still check for time off even with default availability
			const { data: timeOff, error: timeOffError } = await supabase
				.from("employee_time_off")
				.select("*")
				.eq("employee_id", employeeId)
				.eq("status", "approved")
				.lte("start_date", date)
				.gte("end_date", date);

			if (timeOffError) {
				console.error("Error checking employee time off:", timeOffError);
				return false;
			}

			// If no time off, employee is available during default hours
			return !timeOff || timeOff.length === 0;
		}

		return false;
	}

	// Check if the time slot falls within employee's working hours
	const employeeAvailable = availability.some((avail) => {
		const employeeStart = avail.start_time;
		const employeeEnd = avail.end_time;

		// Check if effective dates apply
		if (avail.effective_from && date < avail.effective_from) return false;
		if (avail.effective_until && date > avail.effective_until) return false;

		return startTime >= employeeStart && endTime <= employeeEnd;
	});

	if (!employeeAvailable) {
		return false;
	}

	// Check for time off
	const { data: timeOff, error: timeOffError } = await supabase
		.from("employee_time_off")
		.select("*")
		.eq("employee_id", employeeId)
		.eq("status", "approved")
		.lte("start_date", date)
		.gte("end_date", date);

	if (timeOffError) {
		console.error("Error checking employee time off:", timeOffError);
		return false;
	}

	// If there's time off, check if it conflicts with the time slot
	if (timeOff && timeOff.length > 0) {
		const hasConflict = timeOff.some((off) => {
			// If no specific times, the whole day is off
			if (!off.start_time || !off.end_time) {
				return true;
			}

			// Check if time slot overlaps with time off
			return !(endTime <= off.start_time || startTime >= off.end_time);
		});

		if (hasConflict) {
			return false;
		}
	}

	// Check for existing reservations
	const { data: existingReservations, error: resError } = await supabase
		.from("reservations")
		.select("start_time, end_time")
		.eq("assigned_employee_id", employeeId)
		.eq("status", "confirmed")
		.gte("start_time", `${date}T00:00:00`)
		.lt("start_time", `${date}T23:59:59`);

	if (resError) {
		console.error("Error checking existing reservations:", resError);
		return false;
	}

	// Check for time conflicts with existing reservations
	if (existingReservations && existingReservations.length > 0) {
		const hasConflict = existingReservations.some((res) => {
			const resStart = new Date(res.start_time).toTimeString().slice(0, 5);
			const resEnd = new Date(res.end_time).toTimeString().slice(0, 5);

			return !(endTime <= resStart || startTime >= resEnd);
		});

		if (hasConflict) {
			return false;
		}
	}

	return true;
}

/**
 * Calculate equipment capacity for a service at a specific time
 */
export async function calculateEquipmentCapacity(
	serviceId: string,
	startTime: string,
	endTime: string,
	participantCount: number
): Promise<{ available: boolean; maxCapacity: number; currentReservations: number }> {
	try {
		// Get service equipment requirements
		const { data: serviceEquipment, error: equipmentError } = await supabase
			.from("service_equipment_requirements")
			.select(
				`
        capacity_per_participant,
        equipment (
          id,
          name,
          total_capacity
        )
      `
			)
			.eq("service_id", serviceId);

		if (equipmentError) throw equipmentError;

		if (!serviceEquipment || serviceEquipment.length === 0) {
			// No equipment requirements, use service max participants
			const { data: service } = await supabase
				.from("services")
				.select("max_participants")
				.eq("id", serviceId)
				.single();

			return {
				available: participantCount <= (service?.max_participants || 0),
				maxCapacity: service?.max_participants || 0,
				currentReservations: 0,
			};
		}

		// Calculate capacity for each equipment type
		let minAvailableCapacity = Infinity;
		let totalCurrentReservations = 0;

		for (const req of serviceEquipment) {
			const equipment = req.equipment;
			const capacityNeeded = participantCount * req.capacity_per_participant;

			// Get existing reservations for this equipment during the time period
			const { data: reservations, error: reservationError } = await supabase
				.from("equipment_reservations")
				.select("quantity_reserved")
				.eq("equipment_id", equipment.id)
				.gte("start_time", startTime)
				.lte("end_time", endTime);

			if (reservationError) throw reservationError;

			const currentReserved = reservations?.reduce((sum, res) => sum + res.quantity_reserved, 0) || 0;
			const availableCapacity = equipment.total_capacity - currentReserved;
			const maxParticipants = Math.floor(availableCapacity / req.capacity_per_participant);

			minAvailableCapacity = Math.min(minAvailableCapacity, maxParticipants);
			totalCurrentReservations += currentReserved;
		}

		return {
			available: participantCount <= minAvailableCapacity,
			maxCapacity: minAvailableCapacity === Infinity ? 0 : minAvailableCapacity,
			currentReservations: totalCurrentReservations,
		};
	} catch (error) {
		console.error("Error calculating equipment capacity:", error);
		return { available: false, maxCapacity: 0, currentReservations: 0 };
	}
}

/**
 * Generate dynamic time slots for a service on a specific date
 */
async function generateDynamicTimeSlots(serviceId: string, date: string): Promise<DynamicTimeSlot[]> {
	try {
		// Get service details
		const { data: service, error: serviceError } = await supabase
			.from("services")
			.select("duration_minutes, buffer_time_minutes, max_participants")
			.eq("id", serviceId)
			.single();

		if (serviceError || !service) {
			console.error("Error fetching service:", serviceError);
			return [];
		}

		// Check if date is blacked out
		if (await isDateBlackedOut(serviceId, date)) {
			return [];
		}

		// Check if daily booking limit is reached
		if (await isDailyLimitReached(serviceId, date)) {
			return [];
		}

		// Get scheduling rules
		const rules = await getServiceSchedulingRules(serviceId);

		const dateObj = new Date(date);
		const now = new Date();
		const requestDate = new Date(date);

		if (rules.length === 0) {
			// Create default scheduling rules if none exist
			console.log(`No scheduling rules found for service ${serviceId}, using default rules`);
			const defaultRules: ServiceSchedulingRule[] = [
				{
					id: "default",
					day_of_week: null, // All days
					min_advance_booking_hours: 2,
					max_advance_booking_days: 30,
					operating_start_time: "09:00",
					operating_end_time: "17:00",
					booking_interval_minutes: 60, // Every hour
					specific_times: null,
					max_bookings_per_day: null,
					is_active: true,
				},
			];

			return await generateTimeSlotsFromRules(service, defaultRules, dateObj, now, requestDate);
		}

		return await generateTimeSlotsFromRules(service, rules, dateObj, now, requestDate);
	} catch (error) {
		console.error("Error generating dynamic time slots:", error);
		return [];
	}
}

/**
 * Get available time slots for a service on a specific date
 */
export async function getAvailableTimeSlots(
	serviceId: string,
	date: string,
	participantCount: number = 1
): Promise<TimeSlotWithAvailability[]> {
	try {
		// Generate dynamic time slots
		const dynamicSlots = await generateDynamicTimeSlots(serviceId, date);

		if (dynamicSlots.length === 0) {
			return [];
		}

		// Get qualified employees
		const qualifiedEmployees = await getQualifiedEmployees(serviceId);

		if (qualifiedEmployees.length === 0) {
			return [];
		}

		const slotsWithAvailability: TimeSlotWithAvailability[] = [];

		for (const slot of dynamicSlots) {
			// Check equipment capacity
			const capacity = await calculateEquipmentCapacity(
				serviceId,
				slot.start_time,
				slot.end_time,
				participantCount
			);

			if (!capacity.available || capacity.maxCapacity < participantCount) {
				continue;
			}

			// Find available employee
			const startTimeStr = new Date(slot.start_time).toTimeString().slice(0, 5);
			const endTimeStr = new Date(slot.end_time).toTimeString().slice(0, 5);

			let assignedEmployee: string | undefined;
			for (const employeeId of qualifiedEmployees) {
				if (await isEmployeeAvailable(employeeId, date, startTimeStr, endTimeStr)) {
					assignedEmployee = employeeId;
					break;
				}
			}

			if (!assignedEmployee) {
				continue;
			}

			// Create time slot with availability info
			slotsWithAvailability.push({
				id: `${serviceId}-${date}-${startTimeStr}`, // Generate unique ID
				service_id: serviceId,
				start_time: slot.start_time,
				end_time: slot.end_time,
				status: "available" as const,
				available_capacity: Math.min(capacity.maxCapacity, slot.available_capacity),
				is_available: true,
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
			});
		}

		return slotsWithAvailability;
	} catch (error) {
		console.error("Error getting available time slots:", error);
		return [];
	}
}

/**
 * Check if daily booking limit is reached for a service
 */
async function isDailyLimitReached(serviceId: string, date: string): Promise<boolean> {
	try {
		// Get scheduling rules to check max bookings per day
		const rules = await getServiceSchedulingRules(serviceId);
		const maxBookingsPerDay = rules.find((rule) => rule.max_bookings_per_day)?.max_bookings_per_day;

		if (!maxBookingsPerDay) {
			return false; // No limit set
		}

		// Count existing bookings for this date
		const startOfDay = new Date(date);
		startOfDay.setHours(0, 0, 0, 0);
		const endOfDay = new Date(date);
		endOfDay.setHours(23, 59, 59, 999);

		const { data: existingBookings, error } = await supabase
			.from("reservations")
			.select("id")
			.eq("service_id", serviceId)
			.gte("start_time", startOfDay.toISOString())
			.lte("start_time", endOfDay.toISOString())
			.neq("status", "cancelled");

		if (error) {
			console.error("Error checking daily booking limit:", error);
			return false;
		}

		return (existingBookings?.length || 0) >= maxBookingsPerDay;
	} catch (error) {
		console.error("Error checking daily booking limit:", error);
		return false;
	}
}

/**
 * Check availability for multiple dates
 */
export async function checkAvailability(request: AvailabilityRequest): Promise<AvailabilityResponse> {
	const timeSlots = await getAvailableTimeSlots(request.serviceId, request.date, request.participantCount);

	return {
		date: request.date,
		timeSlots,
	};
}

/**
 * Generate time slots for a service (utility function for admin)
 */
export async function generateTimeSlots(
	serviceId: string,
	date: string,
	startHour: number = 8,
	endHour: number = 18,
	intervalMinutes: number = 60
): Promise<void> {
	try {
		const { data: service, error: serviceError } = await supabase
			.from("services")
			.select("duration_minutes, buffer_time_minutes")
			.eq("id", serviceId)
			.single();

		if (serviceError) throw serviceError;

		const slots = [];
		const baseDate = new Date(date);

		for (let hour = startHour; hour < endHour; hour += intervalMinutes / 60) {
			const startTime = new Date(baseDate);
			startTime.setHours(Math.floor(hour), (hour % 1) * 60, 0, 0);

			const endTime = new Date(startTime);
			endTime.setMinutes(endTime.getMinutes() + service.duration_minutes);

			// Check if end time is within business hours
			if (endTime.getHours() <= endHour) {
				slots.push({
					service_id: serviceId,
					start_time: startTime.toISOString(),
					end_time: endTime.toISOString(),
					status: "available" as const,
				});
			}
		}

		if (slots.length > 0) {
			const { error: insertError } = await supabase.from("time_slots").insert(slots);

			if (insertError) throw insertError;
		}
	} catch (error) {
		console.error("Error generating time slots:", error);
		throw error;
	}
}

/**
 * Reserve equipment for a booking (updated for dynamic time slots)
 */
export async function reserveEquipment(
	serviceId: string,
	startTime: string,
	endTime: string,
	participantCount: number,
	reservationId: string
): Promise<boolean> {
	try {
		// Get service equipment requirements
		const { data: serviceEquipment, error: equipmentError } = await supabase
			.from("service_equipment_requirements")
			.select(
				`
        capacity_per_participant,
        equipment (
          id,
          name,
          total_capacity
        )
      `
			)
			.eq("service_id", serviceId);

		if (equipmentError) throw equipmentError;

		// Create equipment reservations
		const reservations = [];
		for (const req of serviceEquipment || []) {
			const quantityNeeded = participantCount * req.capacity_per_participant;

			reservations.push({
				equipment_id: req.equipment.id,
				reservation_id: reservationId,
				start_time: startTime,
				end_time: endTime,
				quantity_reserved: quantityNeeded,
			});
		}

		if (reservations.length > 0) {
			const { error: reservationError } = await supabase.from("equipment_reservations").insert(reservations);

			if (reservationError) throw reservationError;
		}

		return true;
	} catch (error) {
		console.error("Error reserving equipment:", error);
		return false;
	}
}

/**
 * Generate time slots from scheduling rules (helper function)
 */
async function generateTimeSlotsFromRules(
	service: any,
	rules: ServiceSchedulingRule[],
	dateObj: Date,
	now: Date,
	requestDate: Date
): Promise<DynamicTimeSlot[]> {
	const dayOfWeek = dateObj.getDay();

	// Filter rules for this day of week
	const applicableRules = rules.filter((rule) => rule.day_of_week === null || rule.day_of_week === dayOfWeek);

	if (applicableRules.length === 0) {
		return [];
	}

	const timeSlots: DynamicTimeSlot[] = [];

	for (const rule of applicableRules) {
		// Check advance booking requirements
		const hoursUntilDate = (requestDate.getTime() - now.getTime()) / (1000 * 60 * 60);
		if (hoursUntilDate < rule.min_advance_booking_hours) {
			continue;
		}

		const daysUntilDate = hoursUntilDate / 24;
		if (daysUntilDate > rule.max_advance_booking_days) {
			continue;
		}

		// Generate time slots based on rule type
		if (rule.specific_times && rule.specific_times.length > 0) {
			// Fixed-time scheduling
			for (const timeStr of rule.specific_times) {
				const [hours, minutes] = timeStr.split(":").map(Number);
				const startTime = new Date(dateObj);
				startTime.setHours(hours, minutes, 0, 0);

				const endTime = new Date(startTime);
				endTime.setMinutes(endTime.getMinutes() + service.duration_minutes);

				// Check if within operating hours
				const startTimeStr = timeStr;
				const endTimeStr = endTime.toTimeString().slice(0, 5);

				if (
					rule.operating_start_time &&
					rule.operating_end_time &&
					startTimeStr >= rule.operating_start_time &&
					endTimeStr <= rule.operating_end_time
				) {
					timeSlots.push({
						start_time: startTime.toISOString(),
						end_time: endTime.toISOString(),
						available_capacity: service.max_participants,
						is_available: true,
					});
				}
			}
		} else if (rule.booking_interval_minutes) {
			// Interval-based scheduling
			const [startHour, startMin] = (rule.operating_start_time || "09:00").split(":").map(Number);
			const [endHour, endMin] = (rule.operating_end_time || "17:00").split(":").map(Number);

			const operatingStart = startHour * 60 + startMin;
			const operatingEnd = endHour * 60 + endMin;

			for (
				let currentMin = operatingStart;
				currentMin < operatingEnd;
				currentMin += rule.booking_interval_minutes
			) {
				const startTime = new Date(dateObj);
				startTime.setHours(Math.floor(currentMin / 60), currentMin % 60, 0, 0);

				const endTime = new Date(startTime);
				endTime.setMinutes(endTime.getMinutes() + service.duration_minutes);

				// Check if end time is within operating hours
				const endMinutes = endTime.getHours() * 60 + endTime.getMinutes();
				if (endMinutes <= operatingEnd) {
					timeSlots.push({
						start_time: startTime.toISOString(),
						end_time: endTime.toISOString(),
						available_capacity: service.max_participants,
						is_available: true,
					});
				}
			}
		}
	}

	return timeSlots;
}
