import { calculateEquipmentCapacity, getAvailableTimeSlots } from "./availability";
import { supabase } from "./supabase";
import { BookingFormData, ParticipantInfo, PriceCalculation } from "./types";

export interface ValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

export interface BookingValidationResult extends ValidationResult {
	priceCalculation?: PriceCalculation;
}

/**
 * Validate participant information
 */
export function validateParticipant(participant: ParticipantInfo): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Required fields
	if (!participant.firstName?.trim()) {
		errors.push("Le prénom est requis");
	}
	if (!participant.lastName?.trim()) {
		errors.push("Le nom est requis");
	}
	if (!participant.age || participant.age < 0 || participant.age > 120) {
		errors.push("L'âge doit être entre 0 et 120 ans");
	}

	// Name validation
	if (participant.firstName && participant.firstName.length > 50) {
		errors.push("Le prénom ne peut pas dépasser 50 caractères");
	}
	if (participant.lastName && participant.lastName.length > 50) {
		errors.push("Le nom ne peut pas dépasser 50 caractères");
	}

	// Age-specific warnings
	if (participant.age && participant.age < 3) {
		warnings.push("Les enfants de moins de 3 ans nécessitent une attention particulière");
	}
	if (participant.age && participant.age > 75) {
		warnings.push("Veuillez vous assurer que cette activité convient aux seniors");
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Validate customer information
 */
export function validateCustomerInfo(customerInfo: any): ValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Required fields
	if (!customerInfo.firstName?.trim()) {
		errors.push("Le prénom du client est requis");
	}
	if (!customerInfo.lastName?.trim()) {
		errors.push("Le nom du client est requis");
	}
	if (!customerInfo.email?.trim()) {
		errors.push("L'email est requis");
	}
	if (!customerInfo.phone?.trim()) {
		errors.push("Le téléphone est requis");
	}

	// Email validation
	if (customerInfo.email) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(customerInfo.email)) {
			errors.push("Format d'email invalide");
		}
	}

	// Phone validation (French format)
	if (customerInfo.phone) {
		const phoneRegex = /^(?:\+33|0)[1-9](?:[0-9]{8})$/;
		if (!phoneRegex.test(customerInfo.phone.replace(/\s/g, ""))) {
			warnings.push("Format de téléphone non standard (attendu: format français)");
		}
	}

	// Emergency contact validation
	if (customerInfo.emergencyContactName && !customerInfo.emergencyContactPhone) {
		warnings.push("Numéro de contact d'urgence manquant");
	}
	if (customerInfo.emergencyContactPhone && !customerInfo.emergencyContactName) {
		warnings.push("Nom du contact d'urgence manquant");
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}

/**
 * Calculate pricing for participants
 */
export async function calculateBookingPrice(
	serviceId: string,
	participants: ParticipantInfo[]
): Promise<PriceCalculation> {
	try {
		// Get service pricing tiers
		const { data: pricingTiers, error } = await supabase
			.from("pricing_tiers")
			.select("*")
			.eq("service_id", serviceId)
			.eq("is_active", true)
			.order("min_age");

		if (error) throw error;

		const participantPricing = participants.map((participant) => {
			// Find appropriate pricing tier for participant's age
			const tier = pricingTiers?.find(
				(tier) => participant.age >= tier.min_age && (tier.max_age === null || participant.age <= tier.max_age)
			);

			if (!tier) {
				throw new Error(`Aucun tarif trouvé pour l'âge ${participant.age} ans`);
			}

			return {
				participant,
				tier,
				price: tier.price,
			};
		});

		const subtotal = participantPricing.reduce((sum, item) => sum + item.price, 0);

		return {
			participants: participantPricing,
			subtotal,
			discountAmount: 0, // TODO: Implement discount logic
			total: subtotal,
		};
	} catch (error) {
		console.error("Error calculating booking price:", error);
		throw error;
	}
}

/**
 * Validate complete booking data
 */
export async function validateBooking(bookingData: BookingFormData): Promise<BookingValidationResult> {
	const errors: string[] = [];
	const warnings: string[] = [];

	try {
		// Validate service exists and is active
		console.log("=== BASIC VALIDATION DEBUG START ===");
		console.log("Validating service ID:", bookingData.serviceId);

		const { data: service, error: serviceError } = await supabase
			.from("services")
			.select("id, name, is_active, min_age, max_age, max_participants")
			.eq("id", bookingData.serviceId)
			.single();

		console.log("Service query result:", { service, serviceError });

		if (serviceError || !service) {
			console.log("Service validation failed:", serviceError);
			errors.push("Service non trouvé");
			return { isValid: false, errors, warnings };
		}

		if (!service.is_active) {
			console.log("Service is not active");
			errors.push("Ce service n'est plus disponible");
			return { isValid: false, errors, warnings };
		}

		console.log("Service validation passed");

		// Validate time slot using dynamic availability
		// Extract date from timeSlotId (format: serviceId|date|time)
		const timeSlotParts = bookingData.timeSlotId.split("|");
		if (timeSlotParts.length < 3) {
			errors.push("Format de créneau horaire invalide");
			return { isValid: false, errors, warnings };
		}

		const date = timeSlotParts[1];
		const time = timeSlotParts[2];

		// Get available time slots for this date
		const availableSlots = await getAvailableTimeSlots(
			bookingData.serviceId,
			date,
			bookingData.participants.length
		);

		// Find the specific time slot
		const timeSlot = availableSlots.find((slot) => {
			const slotTime = new Date(slot.start_time).toTimeString().slice(0, 5);
			return slotTime === time;
		});

		if (!timeSlot || !timeSlot.is_available) {
			errors.push("Créneau horaire non disponible");
			return { isValid: false, errors, warnings };
		}

		// Validate participants
		if (!bookingData.participants || bookingData.participants.length === 0) {
			errors.push("Au moins un participant est requis");
			return { isValid: false, errors, warnings };
		}

		if (bookingData.participants.length > service.max_participants) {
			errors.push(`Nombre maximum de participants dépassé (${service.max_participants})`);
		}

		// Validate each participant
		for (let i = 0; i < bookingData.participants.length; i++) {
			const participantValidation = validateParticipant(bookingData.participants[i]);
			if (!participantValidation.isValid) {
				errors.push(...participantValidation.errors.map((err) => `Participant ${i + 1}: ${err}`));
			}
			warnings.push(...participantValidation.warnings.map((warn) => `Participant ${i + 1}: ${warn}`));

			// Check age restrictions
			const age = bookingData.participants[i].age;
			if (age < service.min_age) {
				errors.push(`Participant ${i + 1}: Âge minimum requis ${service.min_age} ans`);
			}
			if (service.max_age && age > service.max_age) {
				errors.push(`Participant ${i + 1}: Âge maximum autorisé ${service.max_age} ans`);
			}
		}

		// Validate customer info
		const customerValidation = validateCustomerInfo(bookingData.customerInfo);
		if (!customerValidation.isValid) {
			errors.push(...customerValidation.errors);
		}
		warnings.push(...customerValidation.warnings);

		// Check equipment capacity
		const capacityCheck = await calculateEquipmentCapacity(
			bookingData.serviceId,
			timeSlot.start_time,
			timeSlot.end_time,
			bookingData.participants.length
		);

		if (!capacityCheck.available) {
			errors.push("Capacité insuffisante pour ce créneau");
		}

		// Calculate pricing
		let priceCalculation: PriceCalculation | undefined;
		if (errors.length === 0) {
			try {
				priceCalculation = await calculateBookingPrice(bookingData.serviceId, bookingData.participants);
			} catch (priceError) {
				errors.push("Erreur lors du calcul du prix");
				console.error("Price calculation error:", priceError);
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings,
			priceCalculation,
		};
	} catch (error) {
		console.error("Error validating booking:", error);
		return {
			isValid: false,
			errors: ["Erreur lors de la validation de la réservation"],
			warnings,
		};
	}
}

/**
 * Validate booking before creation (final check)
 */
export async function validateBookingForCreation(bookingData: BookingFormData): Promise<ValidationResult> {
	console.log("=== VALIDATION FOR CREATION DEBUG START ===");
	console.log("Validating booking data for creation:", {
		serviceId: bookingData.serviceId,
		timeSlotId: bookingData.timeSlotId,
		participantCount: bookingData.participants?.length,
		customerInfo: bookingData.customerInfo,
	});

	console.log("Running basic validation...");
	const validation = await validateBooking(bookingData);
	console.log("Basic validation result:", {
		isValid: validation.isValid,
		errors: validation.errors,
		warnings: validation.warnings,
	});

	// Additional checks for creation
	const errors = [...validation.errors];
	const warnings = [...validation.warnings];

	// Check if time slot is still available using dynamic availability (race condition protection)
	console.log("Checking time slot availability...");
	const timeSlotParts = bookingData.timeSlotId.split("|");
	console.log("TimeSlot parts:", timeSlotParts);

	if (timeSlotParts.length >= 3) {
		const date = timeSlotParts[1];
		const time = timeSlotParts[2];
		console.log("Extracted date:", date, "time:", time);

		try {
			console.log("Fetching available slots for:", {
				serviceId: bookingData.serviceId,
				date,
				participantCount: bookingData.participants.length,
			});

			const availableSlots = await getAvailableTimeSlots(
				bookingData.serviceId,
				date,
				bookingData.participants.length
			);

			console.log("Available slots found:", availableSlots.length);
			console.log(
				"Available slots:",
				availableSlots.map((slot) => ({
					start_time: slot.start_time,
					is_available: slot.is_available,
					available_capacity: slot.available_capacity,
				}))
			);

			const timeSlot = availableSlots.find((slot) => {
				const slotTime = new Date(slot.start_time).toTimeString().slice(0, 5);
				console.log("Comparing slot time:", slotTime, "with requested time:", time);
				return slotTime === time;
			});

			console.log("Found matching time slot:", timeSlot ? "YES" : "NO");

			if (!timeSlot || !timeSlot.is_available) {
				console.log("Time slot validation failed:", {
					timeSlotFound: !!timeSlot,
					isAvailable: timeSlot?.is_available,
				});
				errors.push("Ce créneau n'est plus disponible");
			} else {
				console.log("Time slot validation passed");
			}
		} catch (error) {
			console.error("Error checking time slot availability:", error);
			errors.push("Erreur lors de la vérification de disponibilité");
		}
	} else {
		console.log("Invalid timeSlot format, parts length:", timeSlotParts.length);
		errors.push("Format de créneau horaire invalide");
	}

	console.log("Final validation result:", {
		isValid: errors.length === 0,
		errors,
		warnings,
	});
	console.log("=== VALIDATION FOR CREATION DEBUG END ===");

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
}
