import { reserveEquipmentForTimeSlot } from "@/lib/availability";
import { calculateBookingPrice, validateBookingForCreation } from "@/lib/booking-validation";
import { supabase, supabaseAdmin } from "@/lib/supabase";
import { BookingFormData } from "@/lib/types";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const bookingData: BookingFormData = await request.json();

		// Debug: Log the complete booking data
		console.log("=== BOOKING DEBUG START ===");
		console.log("Received booking data:", JSON.stringify(bookingData, null, 2));
		console.log("TimeSlotId:", bookingData.timeSlotId);
		console.log("TimeSlotId parts:", bookingData.timeSlotId.split("|"));
		console.log("ServiceId:", bookingData.serviceId);
		console.log("Participants:", bookingData.participants);
		console.log("Customer info:", bookingData.customerInfo);

		// Validate booking data
		console.log("Starting validation...");
		const validation = await validateBookingForCreation(bookingData);
		console.log("Validation result:", {
			isValid: validation.isValid,
			errors: validation.errors,
			warnings: validation.warnings,
		});

		if (!validation.isValid) {
			console.log("=== VALIDATION FAILED ===");
			console.log("Errors:", validation.errors);
			console.log("Warnings:", validation.warnings);
			console.log("=== BOOKING DEBUG END ===");
			return NextResponse.json(
				{
					error: "Validation failed",
					details: validation.errors,
					warnings: validation.warnings,
				},
				{ status: 400 }
			);
		}

		console.log("Validation passed, proceeding with booking creation...");

		// Calculate final pricing
		const priceCalculation = await calculateBookingPrice(bookingData.serviceId, bookingData.participants);

		// Create/update customer - need to handle profiles and customers separately
		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase; // Fallback to regular client if admin not available

		// First, check if profile exists by email
		const { data: existingProfile } = await adminClient
			.from("profiles")
			.select("*")
			.eq("email", bookingData.customerInfo.email)
			.single();

		let profile;
		if (existingProfile) {
			// Update existing profile
			const { data: updatedProfile, error: updateError } = await adminClient
				.from("profiles")
				.update({
					first_name: bookingData.customerInfo.firstName,
					last_name: bookingData.customerInfo.lastName,
					phone: bookingData.customerInfo.phone,
				})
				.eq("email", bookingData.customerInfo.email)
				.select()
				.single();

			if (updateError) {
				console.error("Error updating profile:", updateError);
				return NextResponse.json({ error: "Failed to update customer profile" }, { status: 500 });
			}
			profile = updatedProfile;
		} else {
			// Create new profile with generated UUID
			const profileId = generateUUID();
			console.log("Generated profile ID:", profileId);
			const { data: newProfile, error: createError } = await adminClient
				.from("profiles")
				.insert({
					id: profileId,
					email: bookingData.customerInfo.email,
					first_name: bookingData.customerInfo.firstName,
					last_name: bookingData.customerInfo.lastName,
					phone: bookingData.customerInfo.phone,
					role: "customer",
				})
				.select()
				.single();

			if (createError) {
				console.error("Error creating profile:", createError);
				return NextResponse.json({ error: "Failed to create customer profile" }, { status: 500 });
			}
			profile = newProfile;
		}

		// Then, create or update the customer record using the profile ID
		const { data: customer, error: customerError } = await adminClient
			.from("customers")
			.upsert(
				{
					id: profile.id, // Use the profile ID as the customer ID
					emergency_contact_name: bookingData.customerInfo.emergencyContactName,
					emergency_contact_phone: bookingData.customerInfo.emergencyContactPhone,
				},
				{
					onConflict: "id",
				}
			)
			.select()
			.single();

		if (customerError) {
			console.error("Error creating/updating customer:", customerError);
			return NextResponse.json({ error: "Failed to create customer record" }, { status: 500 });
		}

		// Parse dynamic time slot ID to get start and end times
		const timeSlotParts = bookingData.timeSlotId.split("|");
		const date = timeSlotParts[1];
		const time = timeSlotParts[2];

		// Get service duration to calculate end time
		const { data: serviceDetails } = await supabase
			.from("services")
			.select("duration_minutes")
			.eq("id", bookingData.serviceId)
			.single();

		const startTime = new Date(`${date}T${time}:00`);
		const endTime = new Date(startTime);
		endTime.setMinutes(endTime.getMinutes() + (serviceDetails?.duration_minutes || 60));

		// Create reservation
		const reservationData = {
			service_id: bookingData.serviceId,
			customer_id: customer.id,
			start_time: startTime.toISOString(),
			end_time: endTime.toISOString(),
			participant_count: bookingData.participants.length,
			total_amount: priceCalculation.total,
			status: "pending" as const,
			special_requests: bookingData.specialRequests || null,
			qr_code: generateQRCode(), // Simple QR code generation
		};

		const { data: reservation, error: reservationError } = await adminClient
			.from("reservations")
			.insert(reservationData)
			.select()
			.single();

		if (reservationError) {
			console.error("Error creating reservation:", reservationError);
			return NextResponse.json({ error: "Failed to create reservation" }, { status: 500 });
		}

		// Reserve equipment
		const equipmentReserved = await reserveEquipmentForTimeSlot(
			bookingData.serviceId,
			bookingData.timeSlotId,
			bookingData.participants.length
		);

		if (!equipmentReserved) {
			console.error("Failed to reserve equipment");
			// Rollback reservation and participants
			await adminClient.from("reservation_participants").delete().eq("reservation_id", reservation.id);
			await adminClient.from("reservations").delete().eq("id", reservation.id);
			return NextResponse.json({ error: "Failed to reserve equipment" }, { status: 500 });
		}

		// Note: With dynamic availability, we don't need to update time slot status
		// Availability is calculated in real-time based on existing reservations

		// Create notification for admin about new booking
		try {
			const { createNotification, createBookingConfirmationNotification } = await import("@/lib/notifications");

			// Get service and customer details for notification
			const { data: service } = await adminClient
				.from("services")
				.select("name")
				.eq("id", bookingData.serviceId)
				.single();

			if (service) {
				const customerName = `${customer.first_name} ${customer.last_name}`;
				const serviceName = service.name;
				const date = new Date(startTime).toLocaleDateString("fr-FR");

				// Get admin users to notify
				const { data: adminProfiles } = await supabase.from("profiles").select("id").eq("role", "admin");

				// Create notification for each admin
				if (adminProfiles) {
					const notificationTemplate = createBookingConfirmationNotification(
						customerName,
						serviceName,
						date,
						reservation.id
					);

					for (const admin of adminProfiles) {
						await createNotification(admin.id, notificationTemplate, reservation.id);
					}
				}
			}
		} catch (notificationError) {
			console.error("Error creating booking notification:", notificationError);
			// Don't fail the booking if notification fails
		}

		// Return success response
		return NextResponse.json({
			success: true,
			data: {
				reservationId: reservation.id,
				qrCode: reservation.qr_code,
				totalAmount: priceCalculation.total,
				status: "pending",
				message: "Réservation créée avec succès",
			},
			warnings: validation.warnings,
		});
	} catch (error) {
		console.error("Error creating booking:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const customerId = searchParams.get("customer_id");
		const status = searchParams.get("status");
		const limit = parseInt(searchParams.get("limit") || "10");

		const adminClient = supabaseAdmin || supabase; // Fallback to regular client if admin not available

		let query = adminClient
			.from("reservations")
			.select(
				`
        *,
        service:services(name, duration_minutes),
        time_slot:time_slots(start_time, end_time),
        customer:customers(
          id,
          emergency_contact_name,
          emergency_contact_phone,
          profile:profiles!customers_id_fkey(
            first_name,
            last_name,
            email,
            phone
          )
        ),
        participants:reservation_participants(*)
      `
			)
			.order("created_at", { ascending: false })
			.limit(limit);

		if (customerId) {
			query = query.eq("customer_id", customerId);
		}

		if (status) {
			query = query.eq("status", status);
		}

		const { data: reservations, error } = await query;

		if (error) {
			console.error("Error fetching reservations:", error);
			return NextResponse.json({ error: "Failed to fetch reservations" }, { status: 500 });
		}

		return NextResponse.json({
			success: true,
			data: reservations,
		});
	} catch (error) {
		console.error("Error in GET /api/bookings:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}

// Utility function to generate QR code
function generateQRCode(): string {
	const timestamp = Date.now().toString(36);
	const random = Math.random().toString(36).substring(2, 8);
	return `RES-${timestamp}-${random}`.toUpperCase();
}

// Utility function to generate UUID
function generateUUID(): string {
	// Simple UUID v4 generation
	return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0;
		const v = c === "x" ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
}
