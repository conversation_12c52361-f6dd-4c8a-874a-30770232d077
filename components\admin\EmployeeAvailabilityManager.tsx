"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { adminApi } from "@/lib/api-client";
import type { EmployeeAvailability, Employees } from "@/lib/types/employees";
import { Clock, Edit, Plus, Save, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";

const DAYS_OF_WEEK = [
	{ value: 0, label: "Dimanche" },
	{ value: 1, label: "Lundi" },
	{ value: 2, label: "Mardi" },
	{ value: 3, label: "Mercredi" },
	{ value: 4, label: "<PERSON><PERSON>" },
	{ value: 5, label: "Vendredi" },
	{ value: 6, label: "Samedi" },
];

export function EmployeeAvailabilityManager() {
	const [employees, setEmployees] = useState<Employees["Row"][]>([]);
	const [selectedEmployee, setSelectedEmployee] = useState<string>("");
	const [availability, setAvailability] = useState<EmployeeAvailability["Row"][]>([]);
	const [loading, setLoading] = useState(false);
	const [creating, setCreating] = useState(false);
	const [editing, setEditing] = useState<string | null>(null);
	const [formData, setFormData] = useState<Partial<EmployeeAvailability["Insert"]>>({});

	useEffect(() => {
		fetchEmployees();
	}, []);

	useEffect(() => {
		if (selectedEmployee) {
			fetchAvailability();
		}
	}, [selectedEmployee]);

	const fetchEmployees = async () => {
		try {
			const response = await adminApi.getEmployees({ limit: 100 });
			setEmployees(response.data || []);
		} catch (error) {
			console.error("Error fetching employees:", error);
		}
	};

	const fetchAvailability = async () => {
		if (!selectedEmployee) return;

		setLoading(true);
		try {
			// This endpoint doesn't exist yet, we'll need to create it
			const response = await fetch(`/api/admin/employees/${selectedEmployee}/availability`);
			if (response.ok) {
				const data = await response.json();
				setAvailability(data.data || []);
			}
		} catch (error) {
			console.error("Error fetching availability:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleCreate = () => {
		setCreating(true);
		setFormData({
			employee_id: selectedEmployee,
			day_of_week: 1,
			start_time: "08:00",
			end_time: "18:00",
			is_available: true,
		});
	};

	const handleEdit = (item: EmployeeAvailability["Row"]) => {
		setEditing(item.id);
		setFormData({ ...item });
	};

	const handleSave = async () => {
		try {
			if (creating) {
				// Create new availability
				const response = await fetch(`/api/admin/employees/${selectedEmployee}/availability`, {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(formData),
				});
				if (!response.ok) throw new Error("Failed to create availability");
			} else if (editing) {
				// Update existing availability
				const response = await fetch(`/api/admin/employees/${selectedEmployee}/availability/${editing}`, {
					method: "PUT",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify(formData),
				});
				if (!response.ok) throw new Error("Failed to update availability");
			}

			await fetchAvailability();
			handleCancel();
		} catch (error) {
			console.error("Error saving availability:", error);
		}
	};

	const handleDelete = async (id: string) => {
		if (!confirm("Êtes-vous sûr de vouloir supprimer cette disponibilité ?")) return;

		try {
			const response = await fetch(`/api/admin/employees/${selectedEmployee}/availability/${id}`, {
				method: "DELETE",
			});
			if (!response.ok) throw new Error("Failed to delete availability");

			await fetchAvailability();
		} catch (error) {
			console.error("Error deleting availability:", error);
		}
	};

	const handleCancel = () => {
		setCreating(false);
		setEditing(null);
		setFormData({});
	};

	const selectedEmployeeData = employees.find((emp) => emp.id === selectedEmployee);

	return (
		<div className="space-y-6">
			{/* Employee Selection */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Clock className="w-5 h-5" />
						Sélectionner un employé
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div>
							<Label htmlFor="employee">Employé</Label>
							<Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
								<SelectTrigger>
									<SelectValue placeholder="Choisir un employé..." />
								</SelectTrigger>
								<SelectContent>
									{employees.map((employee) => (
										<SelectItem key={employee.id} value={employee.id}>
											{employee.first_name} {employee.last_name} - {employee.role || "Employé"}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Availability Management */}
			{selectedEmployee && (
				<Card>
					<CardHeader>
						<div className="flex items-center justify-between">
							<CardTitle>
								Disponibilités de {selectedEmployeeData?.first_name} {selectedEmployeeData?.last_name}
							</CardTitle>
							<Button onClick={handleCreate} disabled={creating || editing !== null}>
								<Plus className="w-4 h-4 mr-2" />
								Ajouter
							</Button>
						</div>
					</CardHeader>
					<CardContent>
						{loading ? (
							<div className="text-center py-8">Chargement...</div>
						) : (
							<div className="space-y-4">
								{/* Existing Availability */}
								{availability.map((item) => (
									<div key={item.id} className="border rounded-lg p-4">
										{editing === item.id ? (
											<AvailabilityForm
												formData={formData}
												setFormData={setFormData}
												onSave={handleSave}
												onCancel={handleCancel}
											/>
										) : (
											<div className="flex items-center justify-between">
												<div className="space-y-1">
													<div className="flex items-center gap-2">
														<Badge variant="outline">
															{
																DAYS_OF_WEEK.find((d) => d.value === item.day_of_week)
																	?.label
															}
														</Badge>
														<span className="font-medium">
															{item.start_time} - {item.end_time}
														</span>
														<Badge variant={item.is_available ? "default" : "secondary"}>
															{item.is_available ? "Disponible" : "Indisponible"}
														</Badge>
													</div>
													{(item.effective_from || item.effective_until) && (
														<div className="text-sm text-gray-500">
															{item.effective_from && `Du ${item.effective_from}`}
															{item.effective_until && ` au ${item.effective_until}`}
														</div>
													)}
												</div>
												<div className="flex gap-2">
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleEdit(item)}
														disabled={creating || editing !== null}
													>
														<Edit className="w-4 h-4" />
													</Button>
													<Button
														variant="outline"
														size="sm"
														onClick={() => handleDelete(item.id)}
														disabled={creating || editing !== null}
													>
														<Trash2 className="w-4 h-4" />
													</Button>
												</div>
											</div>
										)}
									</div>
								))}

								{/* Create Form */}
								{creating && (
									<div className="border rounded-lg p-4 bg-gray-50">
										<AvailabilityForm
											formData={formData}
											setFormData={setFormData}
											onSave={handleSave}
											onCancel={handleCancel}
										/>
									</div>
								)}

								{availability.length === 0 && !creating && (
									<div className="text-center py-8 text-gray-500">
										<Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
										<p>Aucune disponibilité configurée</p>
										<p className="text-sm">
											Cet employé utilisera les horaires par défaut (8h-18h)
										</p>
									</div>
								)}
							</div>
						)}
					</CardContent>
				</Card>
			)}
		</div>
	);
}

interface AvailabilityFormProps {
	formData: Partial<EmployeeAvailability["Insert"]>;
	setFormData: (data: Partial<EmployeeAvailability["Insert"]>) => void;
	onSave: () => void;
	onCancel: () => void;
}

function AvailabilityForm({ formData, setFormData, onSave, onCancel }: AvailabilityFormProps) {
	return (
		<div className="space-y-4">
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<div>
					<Label htmlFor="day_of_week">Jour de la semaine</Label>
					<Select
						value={formData.day_of_week?.toString()}
						onValueChange={(value) => setFormData({ ...formData, day_of_week: parseInt(value) })}
					>
						<SelectTrigger>
							<SelectValue placeholder="Choisir un jour..." />
						</SelectTrigger>
						<SelectContent>
							{DAYS_OF_WEEK.map((day) => (
								<SelectItem key={day.value} value={day.value.toString()}>
									{day.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div>
					<Label htmlFor="start_time">Heure de début</Label>
					<Input
						type="time"
						value={formData.start_time || ""}
						onChange={(e) => setFormData({ ...formData, start_time: e.target.value })}
					/>
				</div>

				<div>
					<Label htmlFor="end_time">Heure de fin</Label>
					<Input
						type="time"
						value={formData.end_time || ""}
						onChange={(e) => setFormData({ ...formData, end_time: e.target.value })}
					/>
				</div>

				<div className="flex items-center space-x-2">
					<Switch
						checked={formData.is_available || false}
						onCheckedChange={(checked) => setFormData({ ...formData, is_available: checked })}
					/>
					<Label>Disponible</Label>
				</div>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label htmlFor="effective_from">Effectif à partir de (optionnel)</Label>
					<Input
						type="date"
						value={formData.effective_from || ""}
						onChange={(e) => setFormData({ ...formData, effective_from: e.target.value || null })}
					/>
				</div>

				<div>
					<Label htmlFor="effective_until">Effectif jusqu'au (optionnel)</Label>
					<Input
						type="date"
						value={formData.effective_until || ""}
						onChange={(e) => setFormData({ ...formData, effective_until: e.target.value || null })}
					/>
				</div>
			</div>

			<div className="flex gap-2">
				<Button onClick={onSave}>
					<Save className="w-4 h-4 mr-2" />
					Enregistrer
				</Button>
				<Button variant="outline" onClick={onCancel}>
					<X className="w-4 h-4 mr-2" />
					Annuler
				</Button>
			</div>
		</div>
	);
}
